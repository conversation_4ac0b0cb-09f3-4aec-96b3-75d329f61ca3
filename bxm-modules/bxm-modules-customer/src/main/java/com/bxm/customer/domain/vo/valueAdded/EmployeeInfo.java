package com.bxm.customer.domain.vo.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 员工信息轻量级VO
 * 
 * 用于DeliveryOrderVO中的员工信息列表，包含员工的基本信息
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("员工信息VO")
public class EmployeeInfo {

    /** 主键ID */
    @ApiModelProperty(value = "员工ID")
    private Long id;

    /** 增值交付单编号 */
    @ApiModelProperty(value = "增值交付单编号")
    private String deliveryOrderNo;

    /** 业务类型，1-社医保，2-个税明细，3-国税账号，4-个税账号 */
    @ApiModelProperty(value = "业务类型：1-社医保，2-个税明细，3-国税账号，4-个税账号")
    private Integer bizType;

    /** 录入方式，1-批量新增，2-单个新增 */
    @ApiModelProperty(value = "录入方式：1-批量新增，2-单个新增")
    private Integer entryType;

    /** 操作方式 */
    @ApiModelProperty(value = "操作方式")
    private Integer operationType;

    /** 员工姓名 */
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    /** 身份证号 */
    @ApiModelProperty(value = "身份证号")
    private String idNumber;

    /** 手机号 */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /** 应发工资 */
    @ApiModelProperty(value = "应发工资")
    private BigDecimal grossSalary;

    /** 公积金（个人） */
    @ApiModelProperty(value = "公积金（个人）")
    private BigDecimal providentFundPersonal;

    /** 社保基数 */
    @ApiModelProperty(value = "社保基数")
    private BigDecimal socialInsuranceBase;

    /** 社保信息对象 */
    @ApiModelProperty(value = "社保信息对象")
    private SocialInsuranceVO socialInsurance;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 状态，1-待处理，2-已处理，3-已完成 */
    @ApiModelProperty(value = "状态：1-待处理，2-已处理，3-已完成")
    private Integer status;
}
